<svg width="32" height="32" viewBox="0 0 32 32" xmlns="http://www.w3.org/2000/svg">
  <!-- Outer rounded square with gradient -->
  <defs>
    <linearGradient id="bgGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#00BCD4;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#0097A7;stop-opacity:1" />
    </linearGradient>
    <linearGradient id="innerGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#FFFFFF;stop-opacity:0.95" />
      <stop offset="100%" style="stop-color:#F5F5F5;stop-opacity:0.9" />
    </linearGradient>
  </defs>
  
  <!-- Main background rounded rectangle -->
  <rect x="2" y="2" width="28" height="28" rx="6" ry="6" fill="url(#bgGradient)" />
  
  <!-- Inner white rounded rectangle -->
  <rect x="4" y="4" width="24" height="24" rx="4" ry="4" fill="url(#innerGradient)" stroke="#E0E0E0" stroke-width="0.5"/>
  
  <!-- Database/Query comparison elements (simplified for small size) -->
  <!-- Top row -->
  <circle cx="9" cy="11" r="1.5" fill="#B3E5FC" />
  <rect x="12" y="10" width="10" height="2" rx="1" ry="1" fill="#81D4FA" />
  
  <!-- Middle row -->
  <rect x="8" y="15" width="12" height="2" rx="1" ry="1" fill="#4FC3F7" />
  <circle cx="22" cy="16" r="1.5" fill="#B3E5FC" />
  
  <!-- Bottom row -->
  <circle cx="9" cy="21" r="1.5" fill="#B3E5FC" />
  <rect x="12" y="20" width="10" height="2" rx="1" ry="1" fill="#81D4FA" />
</svg>
