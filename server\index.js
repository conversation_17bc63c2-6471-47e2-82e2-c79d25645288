import express from 'express';
import cors from 'cors';
import sql from 'mssql';
import dotenv from 'dotenv';
import axios from 'axios';

dotenv.config();

const app = express();
const PORT = process.env.PORT || 3001;

// Middleware
app.use(cors());
app.use(express.json());

// SQL Server configuration
const dbConfig = {
  server: process.env.DB_SERVER,
  user: process.env.DB_USER,
  password: process.env.DB_PASSWORD,
  domain: '', // Leave empty for SQL Server authentication
  options: {
    encrypt: false, // Use true for Azure SQL
    trustServerCertificate: true, // Use true for local dev / self-signed certs
    enableArithAbort: true,
    instanceName: '', // Leave empty for default instance
    integratedSecurity: false, // Set to true for Windows Authentication
    requestTimeout: 30000,
    connectionTimeout: 30000,
  },
  pool: {
    max: 10,
    min: 0,
    idleTimeoutMillis: 30000
  }
};

// Global connection pool
let pool = null;

// Initialize connection pool
async function initializePool() {
  try {
    pool = await sql.connect(dbConfig);
    console.log(`Connected to SQL Server: ${process.env.DB_ALIAS} (${process.env.DB_SERVER})`);
  } catch (err) {
    console.error('Database connection failed:', err);
  }
}

// Routes

// Test connection
app.get('/api/test-connection', async (req, res) => {
  try {
    if (!pool) {
      await initializePool();
    }
    
    const result = await pool.request().query('SELECT @@VERSION as version, @@SERVERNAME as server_name');
    
    res.json({
      success: true,
      message: 'Connection successful',
      server: process.env.DB_ALIAS,
      data: result.recordset[0]
    });
  } catch (err) {
    console.error('Connection test failed:', err);
    res.status(500).json({
      success: false,
      message: 'Connection failed',
      error: err.message
    });
  }
});

// Get databases
app.get('/api/databases', async (req, res) => {
  try {
    if (!pool) {
      await initializePool();
    }
    
    const result = await pool.request().query(`
      SELECT name as database_name 
      FROM sys.databases 
      WHERE name NOT IN ('master', 'tempdb', 'model', 'msdb')
      ORDER BY name
    `);
    
    res.json({
      success: true,
      databases: result.recordset.map(row => row.database_name)
    });
  } catch (err) {
    console.error('Failed to get databases:', err);
    res.status(500).json({
      success: false,
      message: 'Failed to retrieve databases',
      error: err.message
    });
  }
});

// Execute query
app.post('/api/execute-query', async (req, res) => {
  const { database, query, limit } = req.body;

  if (!database || !query) {
    return res.status(400).json({
      success: false,
      message: 'Database and query are required'
    });
  }

  try {
    if (!pool) {
      await initializePool();
    }

    // Create a new request with the specified database
    const request = pool.request();

    // Switch to the specified database
    await request.query(`USE [${database}]`);

    // First, get the total count if it's a SELECT query
    let totalRows = 0;
    const trimmedQuery = query.trim().toLowerCase();

    if (trimmedQuery.startsWith('select')) {
      try {
        // Create a count query by wrapping the original query
        const countQuery = `SELECT COUNT(*) as total_count FROM (${query}) as count_subquery`;
        const countResult = await request.query(countQuery);
        totalRows = countResult.recordset[0].total_count;
      } catch (countErr) {
        console.warn('Could not get count, proceeding with original query:', countErr.message);
      }
    }

    // Apply limit if specified and it's a SELECT query
    let finalQuery = query;
    if (limit && trimmedQuery.startsWith('select') && !trimmedQuery.includes('top ')) {
      // Add TOP clause if not already present, max 100,000 for performance
      const effectiveLimit = Math.min(limit, 100000);
      finalQuery = query.replace(/^(\s*select\s+)/i, `$1TOP ${effectiveLimit} `);
    }

    // Execute the final query
    const result = await request.query(finalQuery);

    res.json({
      success: true,
      data: result.recordset,
      totalRows: totalRows || result.recordset.length,
      displayedRows: result.recordset.length,
      rowsAffected: result.rowsAffected,
      isLimited: limit && result.recordset.length >= limit
    });
  } catch (err) {
    console.error('Query execution failed:', err);
    res.status(500).json({
      success: false,
      message: 'Query execution failed',
      error: err.message
    });
  }
});

// Get server info
app.get('/api/server-info', (req, res) => {
  res.json({
    success: true,
    server: {
      name: process.env.DB_SERVER,
      alias: process.env.DB_ALIAS,
      user: process.env.DB_USER
    }
  });
});

// Health check
app.get('/api/health', (req, res) => {
  res.json({
    success: true,
    message: 'Server is running',
    timestamp: new Date().toISOString()
  });
});

// Error handling middleware
app.use((err, req, res, next) => {
  console.error(err.stack);
  res.status(500).json({
    success: false,
    message: 'Internal server error',
    error: err.message
  });
});

console.log("ChatGPT endpoint loaded")

// Add ChatGPT proxy endpoint to main app before app.listen
app.post('/api/chatgpt', async (req, res) => {
  const prompt = req.body.prompt;
  try {
    const response = await axios.post(
      'https://api.openai.com/v1/chat/completions',
      {
        model: 'gpt-3.5-turbo',
        messages: [{ role: 'user', content: prompt }],
      },
      {
        headers: {
          'Authorization': `Bearer ${process.env.OPENAI_API_KEY}`,
          'Content-Type': 'application/json',
        },
      }
    );
    res.json(response.data);
  } catch (error) {
    console.error('ChatGPT error:', error.response?.data || error.message);
    res.status(500).json({ error: error.message });
  }
});


// Start server
app.listen(PORT, () => {
  console.log(`Server running on port ${PORT}`);
  console.log(`SQL Server Alias: ${process.env.DB_ALIAS}`);
  initializePool();
});

// Graceful shutdown
process.on('SIGINT', async () => {
  console.log('Shutting down gracefully...');
  if (pool) {
    await pool.close();
  }
  process.exit(0);
});

