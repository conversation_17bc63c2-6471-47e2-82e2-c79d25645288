import React, { useRef, useState, useEffect } from 'react';
import { ResizablePanelGroup, ResizablePanel, ResizableHandle } from '@/components/ui/resizable';
import { Select, SelectTrigger, SelectContent, SelectItem, SelectValue } from '@/components/ui/select';
import { Textarea } from '@/components/ui/textarea';
import { Label } from '@/components/ui/label';
import { Button } from '@/components/ui/button';
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from '@/components/ui/tooltip';
import { Server, Database, Info } from 'lucide-react';
import { useQuery, QueryProvider } from '../context/QueryContext';
import { databaseService } from '../services/databaseService';
import Index from './Index';

const processTypes = [
  { value: 'all', label: 'All' },
  { value: 'individual', label: 'Individual' },
];

const environments = [
  { value: 'dev', label: 'Development' },
  { value: 'qa', label: 'QA' },
  { value: 'prod', label: 'Production' },
];

const queries = [
  { value: '1', label: 'Query 1', query: 'SELECT * FROM users' },
  { value: '2', label: 'Query 2', query: 'SELECT * FROM orders' },
];

const indicators = [
  { title: 'Total Queries', value: 20, color: 'bg-blue-200 text-blue-800' },
  { title: 'UnProcessed', value: 5, color: 'bg-yellow-200 text-yellow-800' },
  { title: 'Processed', value: 15, color: 'bg-green-200 text-green-800' },
];

const aiNames = ['ChatGPT', 'Gemini', 'DeepSeek'];

const MainPageContent = () => {
  const [childPanelSize, setChildPanelSize] = useState(0); // percent
  const [mainPanelSize, setMainPanelSize] = useState(100); // percent
  const [selectedEnvironment, setSelectedEnvironment] = useState(environments[0].value);
  const [selectedProcessType, setSelectedProcessType] = useState(processTypes[0].value);
  const [selectAll, setSelectAll] = useState(false);
  const [selectedQuery, setSelectedQuery] = useState(queries[0].value);
  const [serverInfo, setServerInfo] = useState<{ name: string; alias: string; user: string } | null>(null);
  const [isLoadingServerInfo, setIsLoadingServerInfo] = useState(true);
  const [availableDatabases, setAvailableDatabases] = useState<string[]>([]);
  const [environmentOptions, setEnvironmentOptions] = useState<string[]>([]);
  const [isLoadingEnvironments, setIsLoadingEnvironments] = useState(false);
  const [environmentError, setEnvironmentError] = useState<string | null>(null);
  const groupRef = useRef<any>(null);
  const [environmentValue, setEnvironmentValue] = useState('');
  const [queryResults, setQueryResults] = useState<any[]>([]);
  const [isLoadingQueries, setIsLoadingQueries] = useState(false);
  const [columnWidths, setColumnWidths] = useState({ id: 10, query: 90 });
  const [filterId, setFilterId] = useState('');
  const [filterQuery, setFilterQuery] = useState('');
  const [isResizing, setIsResizing] = useState(false);
  const [startX, setStartX] = useState(0);
  const [startWidth, setStartWidth] = useState(0);
  const [selectedQueryText, setSelectedQueryText] = useState('');
  const [selectedQueryId, setSelectedQueryId] = useState(null);
  const [focusedBox, setFocusedBox] = useState<string | null>(null);
  const [userPrompt, setUserPrompt] = useState('');
  const [aiResults, setAiResults] = useState({ chatgpt: '', gemini: '', deepseek: '' });
  const [isLoadingAI, setIsLoadingAI] = useState({ chatgpt: false, gemini: false, deepseek: false });
  const [practiceOptions, setPracticeOptions] = useState<string[]>([]);
  const [isLoadingPractices, setIsLoadingPractices] = useState(false);
  const [practiceError, setPracticeError] = useState<string | null>(null);
  const [practiceValue, setPracticeValue] = useState('');
  // Helper to get EnvironmentID from EnvironmentName
  const [environmentIdMap, setEnvironmentIdMap] = useState<{[name: string]: number}>({});
  // Helper to get PracticeID from PracticeName
  const [practiceIdMap, setPracticeIdMap] = useState<{[name: string]: number}>({});

  // QueryContext state
  const {
    sourceServer,
    setSourceServer,
    sourceDatabase,
    setSourceDatabase,
  } = useQuery();

  // Handler for panel resize
  const handleLayout = (sizes: number[]) => {
    setMainPanelSize(sizes[0]);
    setChildPanelSize(sizes[1]);
  };

  // Handlers for showing only one panel
  const showMainPage = () => {
    setMainPanelSize(100);
    setChildPanelSize(0);
  };
  const showChildPage = () => {
    setMainPanelSize(0);
    setChildPanelSize(100);
  };
  const showBothPages = () => {
    setMainPanelSize(70);
    setChildPanelSize(30);
  };

  const selectedQueryObj = queries.find(q => q.value === selectedQuery);

  // Load server info and databases on mount
  useEffect(() => {
    const loadServerInfo = async () => {
      try {
        setIsLoadingServerInfo(true);
        const info = await databaseService.getServerInfo();
        setServerInfo(info);
        setSourceServer(info.alias);
      } catch {
        setServerInfo(null);
      } finally {
        setIsLoadingServerInfo(false);
      }
    };
    loadServerInfo();
  }, [setSourceServer]);

  useEffect(() => {
    const loadDatabases = async () => {
      if (sourceServer && serverInfo) {
        try {
          const databases = await databaseService.getDatabases();
          setAvailableDatabases(databases);
        } catch {
          setAvailableDatabases([]);
        }
      }
    };
    loadDatabases();
  }, [sourceServer, serverInfo]);

  // Fetch environments (with IDs) when the selected database is 'Query Comparison'
  useEffect(() => {
    const fetchEnvironments = async () => {
      if (sourceDatabase === 'Query Comparison') {
        setIsLoadingEnvironments(true);
        setEnvironmentError(null);
        try {
          const query = `SELECT EnvironmentID, EnvironmentName FROM dbo.tblEnvironment WHERE IsActive = 1 AND IsDeleted = 0`;
          const result = await databaseService.executeQuery('Query Comparison', query);
          setEnvironmentOptions(result.data.map((row: any) => row.EnvironmentName));
          const idMap: {[name: string]: number} = {};
          result.data.forEach((row: any) => { idMap[row.EnvironmentName] = row.EnvironmentID; });
          setEnvironmentIdMap(idMap);
          if (result.data.length === 0) {
            setEnvironmentError('No environments found in dbo.tblEnvironment.');
          }
        } catch (err) {
          setEnvironmentOptions([]);
          setEnvironmentError('Error fetching environments.');
        }
        setIsLoadingEnvironments(false);
      } else {
        setEnvironmentOptions([]);
        setEnvironmentError(null);
        setEnvironmentIdMap({});
      }
    };
    fetchEnvironments();
  }, [sourceDatabase]);

  // Fetch practices for selected environment (direct relationship)
  useEffect(() => {
    const fetchPracticesForEnvironment = async () => {
      if (
        sourceDatabase === 'Query Comparison' &&
        environmentValue &&
        environmentIdMap[environmentValue]
      ) {
        setIsLoadingPractices(true);
        setPracticeError(null);
        try {
          const envId = environmentIdMap[environmentValue];
          const practiceQuery = `
            SELECT PracticeID, PracticeName FROM dbo.tblGeneralPractice
            WHERE EnvironmentID = ${envId} AND IsActive = 1 AND IsDeleted = 0
          `;
          const practiceResult = await databaseService.executeQuery('Query Comparison', practiceQuery);
          setPracticeOptions(practiceResult.data.map((row: any) => row.PracticeName));
          // Build PracticeName -> PracticeID map
          const idMap: {[name: string]: number} = {};
          practiceResult.data.forEach((row: any) => { idMap[row.PracticeName] = row.PracticeID; });
          setPracticeIdMap(idMap);
          if (practiceResult.data.length === 0) {
            setPracticeError('No active practices found for this environment.');
          }
        } catch (err) {
          setPracticeOptions([]);
          setPracticeError('Error fetching practices.');
          setPracticeIdMap({});
        }
        setIsLoadingPractices(false);
      } else {
        setPracticeOptions([]);
        setPracticeError(null);
        setPracticeIdMap({});
      }
    };
    fetchPracticesForEnvironment();
  }, [sourceDatabase, environmentValue, environmentIdMap]);

  // Fetch queries when environment or practice changes
  useEffect(() => {
    const fetchQueries = async () => {
      if (
        sourceDatabase === 'Query Comparison' &&
        environmentValue &&
        environmentIdMap[environmentValue] &&
        practiceValue &&
        practiceIdMap[practiceValue]
      ) {
        setIsLoadingQueries(true);
        const envId = environmentIdMap[environmentValue];
        const pracId = practiceIdMap[practiceValue];
        const query = `
          SELECT QueryID, OldQueryText
          FROM dbo.tblQuery
          WHERE EnvironmentID = ${envId} AND PracticeID = ${pracId} AND IsActive = 1 AND IsDeleted = 0
        `;
        const result = await databaseService.executeQuery('Query Comparison', query);
        setQueryResults(result.data);
        setIsLoadingQueries(false);
      } else {
        setQueryResults([]);
      }
    };
    fetchQueries();
  }, [sourceDatabase, environmentValue, environmentIdMap, practiceValue, practiceIdMap]);

  // For environmentOptions, filter duplicates
  const uniqueEnvironmentOptions = Array.from(new Set(environmentOptions));

  function colorizeSQL(sql: string) {
    if (!sql) return null;
    // List of ANSI SQL clauses to highlight
    const clauses = [
      'SELECT', 'FROM', 'WHERE', 'INNER JOIN', 'LEFT JOIN', 'RIGHT JOIN', 'FULL JOIN', 'JOIN', 'ON',
      'GROUP BY', 'ORDER BY', 'HAVING', 'UNION', 'UNION ALL', 'LIMIT', 'OFFSET', 'AND', 'OR', 'AS', 'DISTINCT'
    ];
    // Map clause to color
    const clauseColors: Record<string, string> = {
      SELECT: 'text-purple-700 font-bold',
      FROM: 'text-blue-700 font-bold',
      WHERE: 'text-pink-700 font-bold',
      'INNER JOIN': 'text-green-700 font-bold',
      'LEFT JOIN': 'text-green-700 font-bold',
      'RIGHT JOIN': 'text-green-700 font-bold',
      'FULL JOIN': 'text-green-700 font-bold',
      JOIN: 'text-green-700 font-bold',
      ON: 'text-orange-700 font-bold',
      'GROUP BY': 'text-yellow-700 font-bold',
      'ORDER BY': 'text-yellow-700 font-bold',
      HAVING: 'text-pink-700 font-bold',
      UNION: 'text-blue-700 font-bold',
      'UNION ALL': 'text-blue-700 font-bold',
      LIMIT: 'text-red-700 font-bold',
      OFFSET: 'text-red-700 font-bold',
      AND: 'text-blue-600',
      OR: 'text-pink-600',
      AS: 'text-gray-600',
      DISTINCT: 'text-purple-600',
    };
    // Build regex to split on clauses
    const clauseRegex = new RegExp(`\\b(${clauses.sort((a,b)=>b.length-a.length).join('|')})\\b`, 'gi');
    // Split and colorize
    const parts = sql.split(clauseRegex).filter(Boolean);
    return parts.map((part, i) => {
      const upper = part.toUpperCase();
      const color = clauseColors[upper];
      if (color) {
        // Start new line for each clause
        return <span key={i}><br/><span className={color}>{part}</span> </span>;
      }
      return <span key={i}>{part}</span>;
    });
  }

  const sqlQueryBlock = (
    <pre className="whitespace-pre-wrap text-sm flex-1 font-mono">
      {selectedQueryText
        ? colorizeSQL(selectedQueryText)
        : <>
          <span className="text-purple-700 font-bold">DELETE</span>{' '}
          <span className="text-blue-700">FROM</span>{' '}
          <span className="text-green-700">sessions</span>{'\n'}
          <span className="text-blue-700">WHERE</span>{' '}
          <span className="text-green-700">created_at</span>{' '}
          <span className="text-pink-700">&lt;</span>{' '}
          <span className="text-yellow-700">NOW()</span>{' '}
          <span className="text-orange-700">- INTERVAL 30 DAY</span>{' '}
          <span className="text-blue-700">AND</span>{' '}
          <span className="text-green-700">active</span>{' '}
          <span className="text-pink-700">=</span>{' '}
          <span className="text-red-700">0</span>
        </>
      }
    </pre>
  );

  const handleMouseDown = (e: React.MouseEvent) => {
    setIsResizing(true);
    setStartX(e.clientX);
    setStartWidth(columnWidths.id);
    document.body.style.cursor = 'col-resize';
  };

  useEffect(() => {
    const handleMouseMove = (e: MouseEvent) => {
      if (!isResizing) return;
      const dx = e.clientX - startX;
      let newIdWidth = Math.max(5, Math.min(40, startWidth + dx / 4)); // limit between 5% and 40%
      setColumnWidths({ id: newIdWidth, query: 100 - newIdWidth });
    };
    const handleMouseUp = () => {
      setIsResizing(false);
      document.body.style.cursor = '';
    };
    if (isResizing) {
      window.addEventListener('mousemove', handleMouseMove);
      window.addEventListener('mouseup', handleMouseUp);
    }
    return () => {
      window.removeEventListener('mousemove', handleMouseMove);
      window.removeEventListener('mouseup', handleMouseUp);
    };
  }, [isResizing, startX, startWidth]);

  const filteredResults = queryResults.filter(row => {
    const idMatch = filterId === '' || String(row.QueryID || '').includes(filterId);
    const queryMatch = filterQuery === '' || String(row.OldQueryText || '').toLowerCase().includes(filterQuery.toLowerCase());
    return idMatch && queryMatch;
  });

  const handleChatGPT = async () => {
    if (!userPrompt) return;
    setIsLoadingAI(ai => ({ ...ai, chatgpt: true }));
    setAiResults(results => ({ ...results, chatgpt: '' }));
    try {
      const prompt = selectedQueryText
        ? `Query: ${selectedQueryText}\nPrompt: ${userPrompt}`
        : userPrompt;
      const response = await fetch('/api/chatgpt', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ prompt }),
      });
      const data = await response.json();
      setAiResults(results => ({ ...results, chatgpt: data.choices?.[0]?.message?.content || 'No response' }));
    } catch (err) {
      setAiResults(results => ({ ...results, chatgpt: 'Error contacting ChatGPT' }));
    } finally {
      setIsLoadingAI(ai => ({ ...ai, chatgpt: false }));
    }
  };

  return (
    <div className="h-screen w-screen bg-[#e8f0fe] flex flex-col font-sans">
      <ResizablePanelGroup
        direction="horizontal"
        className="flex-1 w-full"
        ref={groupRef}
        onLayout={handleLayout}
        style={{ minWidth: 0 }}
        id="main-child-group"
      >
        {/* Main Page Layout Panel */}
        <ResizablePanel defaultSize={mainPanelSize} minSize={0} maxSize={100} className="h-full" id="main-panel" order={1}>
          {mainPanelSize > 0 && (
            <div className="h-full w-full flex flex-col">
              {/* Top Controls - blue row, styled dropdowns */}
              <div className="flex justify-between items-center mb-4 px-6 pt-6" style={{ height: '10%' }}>
                <div className="flex gap-6 w-full items-center">
                  {/* Source Server Dropdown */}
                  <div>
                    <TooltipProvider>
                      <Tooltip>
                        <TooltipTrigger asChild>
                          <Label htmlFor="source-server" className="text-sm font-semibold text-blue-800 flex items-center gap-2">
                            <Server className="w-4 h-4" />
                            Select Server
                            <Info className="w-3 h-3 text-blue-600 cursor-help" />
                          </Label>
                        </TooltipTrigger>
                        <TooltipContent>
                          <p>Choose the server where your source data is located</p>
                        </TooltipContent>
                      </Tooltip>
                    </TooltipProvider>
                    <Select value={sourceServer} onValueChange={setSourceServer} disabled={isLoadingServerInfo}>
                      <SelectTrigger className="border-2 border-blue-300 focus:border-blue-500 bg-white shadow-md w-[340px]">
                        <SelectValue placeholder="Select Server" />
                      </SelectTrigger>
                      <SelectContent>
                        {isLoadingServerInfo ? (
                          <SelectItem value="loading" disabled>
                            <div className="flex items-center gap-2">
                              <div className="w-4 h-4 animate-spin rounded-full border-2 border-blue-600 border-t-transparent"></div>
                              Loading...
                            </div>
                          </SelectItem>
                        ) : serverInfo ? (
                          <SelectItem key={serverInfo.alias} value={serverInfo.alias}>
                            <div className="flex items-center gap-2">
                              <Server className="w-4 h-4 text-blue-600" />
                              {serverInfo.alias} ({serverInfo.name})
                            </div>
                          </SelectItem>
                        ) : (
                          <SelectItem value="no-server" disabled>
                            <div className="flex items-center gap-2 text-red-600">
                              <Server className="w-4 h-4" />
                              No server available
                            </div>
                          </SelectItem>
                        )}
                      </SelectContent>
                    </Select>
                  </div>
                  {/* Database Dropdown */}
                  <div>
                    <TooltipProvider>
                      <Tooltip>
                        <TooltipTrigger asChild>
                          <Label htmlFor="source-database" className="text-sm font-semibold text-blue-800 flex items-center gap-2">
                            <Database className="w-4 h-4" />
                            Select Database
                            <Info className="w-3 h-3 text-blue-600 cursor-help" />
                          </Label>
                        </TooltipTrigger>
                        <TooltipContent>
                          <p>Select the specific database to query from</p>
                        </TooltipContent>
                      </Tooltip>
                    </TooltipProvider>
                    <Select value={sourceDatabase} onValueChange={setSourceDatabase} disabled={!sourceServer}>
                      <SelectTrigger className="border-2 border-blue-300 focus:border-blue-500 bg-white shadow-md w-[340px]">
                        <SelectValue placeholder="Select Database" />
                      </SelectTrigger>
                      <SelectContent>
                        {availableDatabases.map((database) => (
                          <SelectItem key={database} value={database}>
                            <div className="flex items-center gap-2">
                              <Database className="w-4 h-4 text-blue-600" />
                              {database}
                            </div>
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  </div>
                  {/* Environment Dropdown */}
                  <div>
                    <TooltipProvider>
                      <Tooltip>
                        <TooltipTrigger asChild>
                          <Label htmlFor="select-environment" className="text-sm font-semibold text-blue-800 flex items-center gap-2">
                            <Database className="w-4 h-4" />
                            Select Environment
                            <Info className="w-3 h-3 text-blue-600 cursor-help" />
                          </Label>
                        </TooltipTrigger>
                        <TooltipContent>
                          <p>Select the environment to filter queries</p>
                        </TooltipContent>
                      </Tooltip>
                    </TooltipProvider>
                    <Select
                      value={environmentValue}
                      onValueChange={setEnvironmentValue}
                      disabled={isLoadingEnvironments || uniqueEnvironmentOptions.length === 0}
                    >
                      <SelectTrigger className="border-2 border-blue-300 focus:border-blue-500 bg-white shadow-md w-[340px]">
                        <SelectValue placeholder="Select Environment" />
                      </SelectTrigger>
                      <SelectContent>
                        {uniqueEnvironmentOptions.map(env => (
                          <SelectItem key={env} value={env}>
                            <div className="flex items-center gap-2">
                              <Database className="w-4 h-4 text-blue-600" />
                              {env}
                            </div>
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  </div>
                  {/* Practice Dropdown */}
                  <div>
                    <TooltipProvider>
                      <Tooltip>
                        <TooltipTrigger asChild>
                          <Label htmlFor="select-practice" className="text-sm font-semibold text-blue-800 flex items-center gap-2">
                            <Database className="w-4 h-4" />
                            Select Practice
                            <Info className="w-3 h-3 text-blue-600 cursor-help" />
                          </Label>
                        </TooltipTrigger>
                        <TooltipContent>
                          <p>Select the practice to filter queries</p>
                        </TooltipContent>
                      </Tooltip>
                    </TooltipProvider>
                    <Select
                      value={practiceValue}
                      onValueChange={setPracticeValue}
                      disabled={isLoadingPractices || practiceOptions.length === 0}
                    >
                      <SelectTrigger className="border-2 border-blue-300 focus:border-blue-500 bg-white shadow-md w-[340px]">
                        <SelectValue placeholder="Select Practice" />
                      </SelectTrigger>
                      <SelectContent>
                        {practiceOptions.map(practice => (
                          <SelectItem key={practice} value={practice}>
                            <div className="flex items-center gap-2">
                              <Database className="w-4 h-4 text-blue-600" />
                              {practice}
                            </div>
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  </div>
                  {/* Start Processing Button - align right */}
                  <div className="flex-1 flex justify-end">
                    <button className="bg-orange-500 text-white px-4 py-2 rounded-lg w-44 ml-4">Start Processing</button>
                  </div>
                </div>
              </div>

              {/* Query Table */}
              <div className="flex flex-row w-full px-6" style={{ height: '90%' }}>
                {/* Result Set Table - 80% */}
                <div className="flex flex-col flex-1" style={{width: '100%'}}>
                  <div className="bg-white rounded shadow p-4 flex-shrink-0" style={{overflow: 'auto', flex: '0 0 auto', height: '70%'}}>
                    <div className="flex justify-between items-center mb-2">
                      <div className="flex gap-4 text-sm w-full">
                        <span className="text-blue-600">● Total Rows: {isLoadingQueries ? '...' : filteredResults.length}</span>
                        <span className="ml-auto flex gap-3">
                          <span className="inline-flex items-center px-3 py-1 rounded-full bg-green-100 text-green-800 font-semibold text-xs border border-green-300">Processed Queries</span>
                          <span className="inline-flex items-center px-3 py-1 rounded-full bg-yellow-100 text-yellow-800 font-semibold text-xs border border-yellow-300">UnProcessed Queries</span>
                        </span>
                      </div>
                    </div>
                    <div className="table-scroll-container" style={{maxHeight: '93%'}}>
                      <table className="w-full text-sm min-w-[600px]" style={{tableLayout: 'fixed'}}>
                        <colgroup>
                          <col style={{width: `${columnWidths.id}%`}} />
                          <col style={{width: `${columnWidths.query}%`}} />
                        </colgroup>
                        <thead>
                          <tr className="bg-gray-100">
                            <th className="p-2 text-left select-none" style={{userSelect: 'none', whiteSpace: 'nowrap', position: 'relative'}}>
                              Query ID
                              <span
                                style={{position: 'absolute', right: 0, top: 0, bottom: 0, width: 6, cursor: 'col-resize', zIndex: 10}}
                                onMouseDown={handleMouseDown}
                              >
                                <span className="block h-full w-1 bg-blue-300 hover:bg-blue-500 transition" />
                              </span>
                            </th>
                            <th className="p-2 text-left select-none" style={{userSelect: 'none', whiteSpace: 'nowrap'}}>Old Query</th>
                          </tr>
                        </thead>
                        <tbody>
                          {isLoadingQueries ? (
                            <tr><td colSpan={2} className="p-2 text-center text-gray-400">Loading queries...</td></tr>
                          ) : filteredResults.length > 0 ? (
                            filteredResults.map((row, idx) => (
                              <tr
                                key={row.QueryID || idx}
                                className={selectedQueryId === row.QueryID ? 'bg-blue-100' : ''}
                                style={{cursor: 'pointer'}}
                                onClick={() => {
                                  setSelectedQueryText(row.OldQueryText || '');
                                  setSelectedQueryId(row.QueryID);
                                }}
                              >
                                <td className="p-2 whitespace-nowrap" style={{verticalAlign: 'top'}}>{row.QueryID || idx + 1}</td>
                                <td className="p-2 whitespace-nowrap" style={{verticalAlign: 'top'}}>{row.OldQueryText || ''}</td>
                              </tr>
                            ))
                          ) : (
                            <tr><td colSpan={2} className="p-2 text-center text-gray-400">No queries found.</td></tr>
                          )}
                        </tbody>
                      </table>
                    </div>
                  </div>
                  {/* Buttons and AI Tools row, same width as table */}
                  <div className="w-full flex flex-row gap-4 items-start" style={{height: '100%'}}>
                    {/* Prompt and ChatGPT side by side */}
                    <div className="flex flex-row gap-4 flex-1 mt-6">
                      {/* User Prompt */}
                      <div className={`bg-blue-50 rounded shadow p-4 flex flex-col min-h-[213px] flex-1 transition-all border ${focusedBox === 'user' ? 'border-blue-400' : 'border-transparent'}`} style={{marginBottom: 0}}>
                        <div className="font-bold mb-2 text-blue-900">What can I help with?</div>
                        <textarea 
                          className="border border-blue-200 focus:border-blue-400 hover:border-blue-300 rounded p-2 flex-1 text-xs mb-2 placeholder:text-blue-400 bg-blue-100 text-blue-900" 
                          placeholder="Enter your prompt here..." 
                          onFocus={() => setFocusedBox('user')} 
                          onBlur={() => setFocusedBox(null)}
                          value={userPrompt}
                          onChange={e => setUserPrompt(e.target.value)}
                          style={{minHeight: '102px'}}
                        />
                      </div>
                      {/* ChatGPT */}
                      <div className={`bg-white rounded shadow p-4 flex flex-col min-h-[213px] flex-1 transition-all border ${focusedBox === 'chatgpt' ? 'border-blue-500' : 'border-transparent'}`} style={{marginBottom: 0}}>
                        <button className="bg-blue-500 text-white px-2 py-1 rounded mb-2 self-start" onClick={handleChatGPT} disabled={isLoadingAI.chatgpt || !userPrompt}>
                          {isLoadingAI.chatgpt ? 'Loading...' : 'Generate ChatGPT Prompt'}
                        </button>
                        <textarea
                          className="border border-gray-300 focus:border-transparent hover:border-gray-300 rounded p-2 flex-1 text-xs mb-2 placeholder:text-slate-400"
                          placeholder="ChatGPT result will appear here..."
                          value={isLoadingAI.chatgpt ? 'Loading...' : aiResults.chatgpt}
                          readOnly
                          onFocus={() => setFocusedBox('chatgpt')}
                          onBlur={() => setFocusedBox(null)}
                          style={{minHeight: '102px'}}
                        />
                      </div>
                    </div>
                  </div>
                </div>
                {/* Query Section - 20% */}
                <div className="bg-gray-100 rounded p-4 shadow-lg flex flex-col justify-start ml-4 min-h-[250px] overflow-y-auto overflow-x-hidden scrollbar-thin scrollbar-thumb-blue-400 scrollbar-track-blue-100" style={{width: '20%', minWidth: 200, maxWidth: 400, height: '100%'}}>
                  {selectedQueryText ? (
                    <pre className="whitespace-pre-wrap font-mono flex-1 overflow-y-auto overflow-x-hidden scrollbar-thin scrollbar-thumb-blue-400 scrollbar-track-blue-100 text-sm" style={{maxWidth: '100%', maxHeight: '100%'}}>
                      {colorizeSQL(selectedQueryText)}
                    </pre>
                  ) : (
                    <div className="flex flex-col items-center justify-center h-full w-full text-gray-400">
                      <svg xmlns="http://www.w3.org/2000/svg" className="h-16 w-16 mb-2" fill="none" viewBox="0 0 24 24" stroke="currentColor" strokeWidth={1.5}>
                        <path strokeLinecap="round" strokeLinejoin="round" d="M12 20c4.418 0 8-1.79 8-4V8c0-2.21-3.582-4-8-4S4 5.79 4 8v8c0 2.21 3.582 4 8 4z" />
                        <path strokeLinecap="round" strokeLinejoin="round" d="M4 8c0 2.21 3.582 4 8 4s8-1.79 8-4" />
                      </svg>
                      <span className="text-lg font-semibold">No query selected</span>
                      <span className="text-xs mt-1">Select a query from the table to view its details here.</span>
                    </div>
                  )}
                </div>
              </div>
            </div>
          )}
        </ResizablePanel>
        <ResizableHandle withHandle />
        {/* Child Page Panel */}
        <ResizablePanel defaultSize={childPanelSize} minSize={0} maxSize={100} className="h-full" id="child-panel" order={2}>
          {childPanelSize > 0 && <Index />}
        </ResizablePanel>
      </ResizablePanelGroup>
    </div>
  );
};

const MainPage = () => (
  <QueryProvider>
    <MainPageContent />
  </QueryProvider>
);

export default MainPage; 