import React, { useMemo, useEffect, useRef, useState, useCallback } from 'react';
import { X, ChevronLeft, ChevronRight, Eye, Filter } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { useQuery } from '../context/QueryContext';
import DetailedResultsTable from './DetailedResultsTable';
import { ResizablePanelGroup, ResizablePanel, ResizableHandle } from '@/components/ui/resizable';

const DetailedReviewModal = () => {
  const {
    isDetailedReviewOpen,
    setIsDetailedReviewOpen,
    sourceResults,
    destinationResults,
    currentReviewRowIndex,
    setCurrentReviewRowIndex,
  } = useQuery();

  const sourceTableRef = useRef<any>(null);
  const destinationTableRef = useRef<any>(null);

  // Filter state for display rows
  const [displayFilter, setDisplayFilter] = useState<'all' | 'matched' | 'unmatched'>('all');

  // State for smooth row alignment animations
  const [isTransitioning, setIsTransitioning] = useState(false);
  const [pendingRowIndex, setPendingRowIndex] = useState<number | null>(null);

  // Helper function to determine if a row is matched
  const isRowMatched = useCallback((rowIndex: number) => {
    if (!sourceResults || !destinationResults) return false;

    const sourceRow = sourceResults.data[rowIndex];
    const destinationRow = destinationResults.data[rowIndex];

    if (!sourceRow || !destinationRow) return false;

    return JSON.stringify(sourceRow) === JSON.stringify(destinationRow);
  }, [sourceResults, destinationResults]);

  // Get filtered row indices based on display filter
  const filteredRowIndices = useMemo(() => {
    if (!sourceResults || !destinationResults) return [];

    const maxRows = Math.max(sourceResults.data.length, destinationResults.data.length);
    const allIndices = Array.from({ length: maxRows }, (_, i) => i);

    if (displayFilter === 'all') {
      return allIndices;
    }

    return allIndices.filter(index => {
      const isMatched = isRowMatched(index);
      return displayFilter === 'matched' ? isMatched : !isMatched;
    });
  }, [sourceResults, destinationResults, displayFilter, isRowMatched]);

  // Calculate comparison statistics
  const comparisonStats = useMemo(() => {
    if (!sourceResults || !destinationResults) {
      return { totalRows: 0, matchedRows: 0, unmatchedRows: 0 };
    }

    let matchedRows = 0;
    let unmatchedRows = 0;
    const maxRows = Math.max(sourceResults.data.length, destinationResults.data.length);

    for (let i = 0; i < maxRows; i++) {
      const sourceRow = sourceResults.data[i];
      const destinationRow = destinationResults.data[i];

      if (!sourceRow || !destinationRow) {
        unmatchedRows++;
      } else {
        const isMatch = JSON.stringify(sourceRow) === JSON.stringify(destinationRow);
        if (isMatch) {
          matchedRows++;
        } else {
          unmatchedRows++;
        }
      }
    }

    return { totalRows: maxRows, matchedRows, unmatchedRows };
  }, [sourceResults, destinationResults]);

  // Reset row index when modal opens
  useEffect(() => {
    if (isDetailedReviewOpen) {
      setCurrentReviewRowIndex(0);
    }
  }, [isDetailedReviewOpen, setCurrentReviewRowIndex]);

  // Reset row index when display filter changes with smooth transition
  useEffect(() => {
    if (filteredRowIndices.length > 0) {
      // Start transition animation
      setIsTransitioning(true);

      // Add a small delay to allow for smooth transition
      const timer = setTimeout(() => {
        setCurrentReviewRowIndex(0);
        setIsTransitioning(false);
      }, 200);
      return () => clearTimeout(timer);
    }
  }, [displayFilter, setCurrentReviewRowIndex, filteredRowIndices.length]);

  // Navigation functions
  const handleNext = () => {
    if (currentReviewRowIndex < filteredRowIndices.length - 1) {
      setCurrentReviewRowIndex(currentReviewRowIndex + 1);
    }
  };

  const handlePrevious = () => {
    if (currentReviewRowIndex > 0) {
      setCurrentReviewRowIndex(currentReviewRowIndex - 1);
    }
  };

  // Keyboard navigation
  useEffect(() => {
    if (!isDetailedReviewOpen) return;

    const handleKeyDown = (event: KeyboardEvent) => {
      switch (event.key) {
        case 'ArrowLeft':
        case 'ArrowUp':
          event.preventDefault();
          handlePrevious();
          break;
        case 'ArrowRight':
        case 'ArrowDown':
          event.preventDefault();
          handleNext();
          break;
        case 'Escape':
          event.preventDefault();
          handleClose();
          break;
      }
    };

    document.addEventListener('keydown', handleKeyDown);
    return () => document.removeEventListener('keydown', handleKeyDown);
  }, [isDetailedReviewOpen, currentReviewRowIndex, filteredRowIndices]);

  const handleClose = () => {
    setIsDetailedReviewOpen(false);
  };

  // Scroll callback to ensure highlighted rows are visible
  const handleScrollToRow = (rowIndex: number) => {
    // This will be handled by the individual table components
    // through their internal scrollToRow functionality
  };

  // Function to find matching row in the other dataset
  const findMatchingRow = (clickedRowData: Record<string, any>, targetDataset: any[]) => {
    if (!targetDataset) return -1;

    // Try to find exact match first
    const exactMatchIndex = targetDataset.findIndex(row =>
      JSON.stringify(row) === JSON.stringify(clickedRowData)
    );

    if (exactMatchIndex !== -1) return exactMatchIndex;

    // If no exact match, try to find by primary key (first column)
    const primaryKey = Object.keys(clickedRowData)[0];
    if (primaryKey) {
      const primaryKeyMatchIndex = targetDataset.findIndex(row =>
        row[primaryKey] === clickedRowData[primaryKey]
      );
      if (primaryKeyMatchIndex !== -1) return primaryKeyMatchIndex;
    }

    return -1; // No match found
  };

  // Handle row click from either table to align both tables to the same row
  const handleRowClick = (clickedRowIndex: number, clickedRowData: Record<string, any>, sourceTable: 'source' | 'destination') => {
    // Set the currentReviewRowIndex to the clicked row index in filteredRowIndices
    setCurrentReviewRowIndex(clickedRowIndex);
  };

  if (!isDetailedReviewOpen) return null;

  return (
    <div className="fixed inset-0 z-50 bg-black/50 backdrop-blur-sm flex items-center justify-center">
      {/* Modal Container with 5% margin on all sides */}
      <div
        className="bg-white rounded-xl shadow-2xl flex flex-col overflow-hidden"
        style={{
          width: '90%',
          height: '90%',
          margin: '5%',
          maxHeight: '90vh'
        }}
      >
        {/* Header */}
        <div className="flex items-center justify-between p-4 border-b border-slate-200 bg-gradient-to-r from-purple-50 via-pink-50 to-blue-50">
          <div className="flex items-center gap-6">
            <div className="flex items-center gap-3">
              <div className="p-2 bg-gradient-to-r from-purple-500 to-pink-500 rounded-lg">
                <Eye className="w-5 h-5 text-white" />
              </div>
              <div>
                <h2 className="text-xl font-bold text-slate-800">Detailed Review</h2>
                <p className="text-xs text-slate-600">Side-by-side comparison analysis</p>
              </div>
            </div>

            {/* Statistics */}
            <div className="flex items-center gap-4 text-sm">
              <div className="flex items-center gap-2 bg-blue-100 px-3 py-1 rounded-full">
                <div className="w-2 h-2 bg-blue-500 rounded-full"></div>
                <span className="font-medium text-blue-800">Total Rows: {comparisonStats.totalRows}</span>
              </div>
              <div className="flex items-center gap-2 bg-green-100 px-3 py-1 rounded-full">
                <div className="w-2 h-2 bg-green-500 rounded-full"></div>
                <span className="font-medium text-green-800">Matched: {comparisonStats.matchedRows}</span>
              </div>
              <div className="flex items-center gap-2 bg-red-100 px-3 py-1 rounded-full">
                <div className="w-2 h-2 bg-red-500 rounded-full"></div>
                <span className="font-medium text-red-800">Unmatched: {comparisonStats.unmatchedRows}</span>
              </div>
            </div>
          </div>

          <div className="flex items-center gap-3">
            {/* Display Rows Filter */}
            <div className="flex items-center gap-2">
              <Filter className="w-4 h-4 text-slate-600" />
              <span className="text-sm font-medium text-slate-700">Display Rows:</span>
              <Select value={displayFilter} onValueChange={(value: 'all' | 'matched' | 'unmatched') => setDisplayFilter(value)}>
                <SelectTrigger className="w-32 h-8 text-xs">
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All</SelectItem>
                  <SelectItem value="matched">Matched</SelectItem>
                  <SelectItem value="unmatched">Unmatched</SelectItem>
                </SelectContent>
              </Select>
            </div>

            <Button
              onClick={handleClose}
              variant="ghost"
              size="sm"
              className="hover:bg-red-100 hover:text-red-600 transition-colors"
            >
              <X className="w-5 h-5" />
            </Button>
          </div>
        </div>

        {/* Main Content - Full height */}
        <div className="flex-1 flex p-4 min-h-0" style={{ gap: '10px' }}>
          <ResizablePanelGroup direction="horizontal" className="flex-1 flex min-h-0 gap-2">
            <ResizablePanel defaultSize={50} minSize={20} maxSize={80} className="flex flex-col bg-blue-50 rounded-lg shadow-lg border-2 border-blue-300">
              <div className="p-4 border-b-2 border-blue-300 bg-gradient-to-r from-blue-100 to-blue-200">
                <h3 className="font-bold text-blue-900 text-lg">📊 Source Result Set</h3>
              </div>
              <div className="flex-1 min-h-0">
                <DetailedResultsTable
                  results={sourceResults}
                  loading={false}
                  type="source"
                  currentRowIndex={filteredRowIndices[currentReviewRowIndex] || 0}
                  onScrollToRow={handleScrollToRow}
                  onRowClick={(rowIndex, rowData) => {
                    handleRowClick(rowIndex, rowData, 'source');
                  }}
                  displayFilter="all"
                  visibleRowIndices={filteredRowIndices}
                  isTransitioning={isTransitioning}
                  pendingRowIndex={pendingRowIndex !== null ? filteredRowIndices[pendingRowIndex] || 0 : null}
                />
              </div>
            </ResizablePanel>
            <ResizableHandle withHandle />
            <ResizablePanel defaultSize={50} minSize={20} maxSize={80} className="flex flex-col bg-green-50 rounded-lg shadow-lg border-2 border-green-300">
              <div className="p-4 border-b-2 border-green-300 bg-gradient-to-r from-green-100 to-green-200">
                <h3 className="font-bold text-green-900 text-lg">🎯 Destination Result Set</h3>
              </div>
              <div className="flex-1 min-h-0">
                <DetailedResultsTable
                  results={destinationResults}
                  loading={false}
                  type="destination"
                  currentRowIndex={filteredRowIndices[currentReviewRowIndex] || 0}
                  onScrollToRow={handleScrollToRow}
                  onRowClick={(rowIndex, rowData) => {
                    handleRowClick(rowIndex, rowData, 'destination');
                  }}
                  displayFilter="all"
                  visibleRowIndices={filteredRowIndices}
                  isTransitioning={isTransitioning}
                  pendingRowIndex={pendingRowIndex !== null ? filteredRowIndices[pendingRowIndex] || 0 : null}
                />
              </div>
            </ResizablePanel>
          </ResizablePanelGroup>
        </div>

        {/* Navigation Controls */}
        <div className="flex items-center justify-center gap-6 p-4 bg-gradient-to-r from-slate-50 via-blue-50 to-purple-50 border-t-2 border-slate-300 shadow-lg" style={{ minHeight: '80px' }}>
          <Button
            onClick={handlePrevious}
            disabled={currentReviewRowIndex === 0}
            className="flex items-center gap-2 bg-gradient-to-r from-blue-600 to-blue-700 hover:from-blue-700 hover:to-blue-800 text-white disabled:opacity-50 disabled:transform-none transform hover:scale-105 transition-all duration-200 px-6 py-3 rounded-xl shadow-lg"
          >
            <ChevronLeft className="w-5 h-5" />
            Previous
          </Button>

          <div className="text-center bg-white/70 backdrop-blur-sm rounded-xl p-4 border border-white/50 shadow-md">
            <div className="text-lg font-bold text-slate-800">
              Row {currentReviewRowIndex + 1} of {filteredRowIndices.length}
            </div>
            <div className="text-sm text-slate-600 mt-1">
              Navigate through {displayFilter === 'all' ? 'all' : displayFilter} result sets
            </div>
          </div>

          <Button
            onClick={handleNext}
            disabled={currentReviewRowIndex >= filteredRowIndices.length - 1}
            className="flex items-center gap-2 bg-gradient-to-r from-blue-600 to-blue-700 hover:from-blue-700 hover:to-blue-800 text-white disabled:opacity-50 disabled:transform-none transform hover:scale-105 transition-all duration-200 px-6 py-3 rounded-xl shadow-lg"
          >
            Next
            <ChevronRight className="w-5 h-5" />
          </Button>
        </div>
      </div>
    </div>
  );
};

export default DetailedReviewModal;
