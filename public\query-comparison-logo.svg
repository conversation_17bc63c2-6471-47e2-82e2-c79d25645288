<svg width="200" height="200" viewBox="0 0 200 200" xmlns="http://www.w3.org/2000/svg">
  <!-- Outer rounded square with gradient -->
  <defs>
    <linearGradient id="bgGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#00BCD4;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#0097A7;stop-opacity:1" />
    </linearGradient>
    <linearGradient id="innerGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#FFFFFF;stop-opacity:0.95" />
      <stop offset="100%" style="stop-color:#F5F5F5;stop-opacity:0.9" />
    </linearGradient>
  </defs>
  
  <!-- Main background rounded rectangle -->
  <rect x="10" y="10" width="180" height="180" rx="35" ry="35" fill="url(#bgGradient)" />
  
  <!-- Inner white rounded rectangle -->
  <rect x="25" y="25" width="150" height="150" rx="25" ry="25" fill="url(#innerGradient)" stroke="#E0E0E0" stroke-width="1"/>
  
  <!-- TM symbol -->
  <text x="165" y="35" font-family="Arial, sans-serif" font-size="14" font-weight="bold" fill="#00BCD4">TM</text>
  
  <!-- Database/Query comparison elements -->
  <!-- Top row - circle and bars -->
  <circle cx="55" cy="65" r="8" fill="#B3E5FC" />
  <rect x="75" y="60" width="60" height="10" rx="5" ry="5" fill="#81D4FA" />
  
  <!-- Middle row - longer bar and circle -->
  <rect x="45" y="90" width="80" height="12" rx="6" ry="6" fill="#4FC3F7" />
  <circle cx="140" cy="96" r="8" fill="#B3E5FC" />
  
  <!-- Bottom row - circle and bars -->
  <circle cx="55" cy="125" r="8" fill="#B3E5FC" />
  <rect x="75" y="120" width="60" height="10" rx="5" ry="5" fill="#81D4FA" />
  
  <!-- Additional decorative elements for comparison theme -->
  <rect x="45" y="150" width="110" height="2" rx="1" ry="1" fill="#E1F5FE" />
  <rect x="45" y="155" width="90" height="2" rx="1" ry="1" fill="#E1F5FE" />
</svg>
