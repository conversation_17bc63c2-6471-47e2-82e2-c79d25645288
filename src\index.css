@tailwind base;
@tailwind components;
@tailwind utilities;

/* Definition of the design system. All colors, gradients, fonts, etc should be defined here. */

@layer base {
  :root {
    --background: 0 0% 100%;
    --foreground: 222.2 84% 4.9%;

    --card: 0 0% 100%;
    --card-foreground: 222.2 84% 4.9%;

    --popover: 0 0% 100%;
    --popover-foreground: 222.2 84% 4.9%;

    --primary: 222.2 47.4% 11.2%;
    --primary-foreground: 210 40% 98%;

    --secondary: 210 40% 96.1%;
    --secondary-foreground: 222.2 47.4% 11.2%;

    --muted: 210 40% 96.1%;
    --muted-foreground: 215.4 16.3% 46.9%;

    --accent: 210 40% 96.1%;
    --accent-foreground: 222.2 47.4% 11.2%;

    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 210 40% 98%;

    --border: 214.3 31.8% 91.4%;
    --input: 214.3 31.8% 91.4%;
    --ring: 222.2 84% 4.9%;

    --radius: 0.5rem;

    --sidebar-background: 0 0% 98%;

    --sidebar-foreground: 240 5.3% 26.1%;

    --sidebar-primary: 240 5.9% 10%;

    --sidebar-primary-foreground: 0 0% 98%;

    --sidebar-accent: 240 4.8% 95.9%;

    --sidebar-accent-foreground: 240 5.9% 10%;

    --sidebar-border: 220 13% 91%;

    --sidebar-ring: 217.2 91.2% 59.8%;
  }

  .dark {
    --background: 222.2 84% 4.9%;
    --foreground: 210 40% 98%;

    --card: 222.2 84% 4.9%;
    --card-foreground: 210 40% 98%;

    --popover: 222.2 84% 4.9%;
    --popover-foreground: 210 40% 98%;

    --primary: 210 40% 98%;
    --primary-foreground: 222.2 47.4% 11.2%;

    --secondary: 217.2 32.6% 17.5%;
    --secondary-foreground: 210 40% 98%;

    --muted: 217.2 32.6% 17.5%;
    --muted-foreground: 215 20.2% 65.1%;

    --accent: 217.2 32.6% 17.5%;
    --accent-foreground: 210 40% 98%;

    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 210 40% 98%;

    --border: 217.2 32.6% 17.5%;
    --input: 217.2 32.6% 17.5%;
    --ring: 212.7 26.8% 83.9%;
    --sidebar-background: 240 5.9% 10%;
    --sidebar-foreground: 240 4.8% 95.9%;
    --sidebar-primary: 224.3 76.3% 48%;
    --sidebar-primary-foreground: 0 0% 100%;
    --sidebar-accent: 240 3.7% 15.9%;
    --sidebar-accent-foreground: 240 4.8% 95.9%;
    --sidebar-border: 240 3.7% 15.9%;
    --sidebar-ring: 217.2 91.2% 59.8%;
  }
  
}

@layer base {
  * {
    @apply border-border;
  }

  body {
    @apply bg-background text-foreground;
  }
}

/* Custom scrollbar styles for better visibility */
@layer utilities {
  /* Always show horizontal scrollbar */
  .scrollbar-always-visible {
    scrollbar-width: thin;
    scrollbar-color: #cbd5e1 #f1f5f9;
  }

  .scrollbar-always-visible::-webkit-scrollbar {
    height: 12px;
    width: 12px;
  }

  .scrollbar-always-visible::-webkit-scrollbar-track {
    background: #f1f5f9;
    border-radius: 6px;
  }

  .scrollbar-always-visible::-webkit-scrollbar-thumb {
    background: #cbd5e1;
    border-radius: 6px;
    border: 2px solid #f1f5f9;
  }

  .scrollbar-always-visible::-webkit-scrollbar-thumb:hover {
    background: #94a3b8;
  }

  /* Force horizontal scrollbar to always be visible */
  .force-horizontal-scrollbar {
    overflow-x: scroll !important;
  }

  /* Ensure horizontal scrollbar is always visible on table containers */
  .table-scroll-container {
    overflow-x: scroll !important;
    overflow-y: scroll !important;
    scrollbar-width: thin;
    scrollbar-color: #cbd5e1 #f1f5f9;
  }

  .table-scroll-container::-webkit-scrollbar {
    height: 14px;
    width: 14px;
    display: block !important;
  }

  .table-scroll-container::-webkit-scrollbar-track {
    background: #f1f5f9;
    border-radius: 7px;
  }

  .table-scroll-container::-webkit-scrollbar-thumb {
    background: #cbd5e1;
    border-radius: 7px;
    border: 2px solid #f1f5f9;
  }

  .table-scroll-container::-webkit-scrollbar-thumb:hover {
    background: #94a3b8;
  }

  /* Force scrollbar visibility even when content fits */
  .table-scroll-container::-webkit-scrollbar-corner {
    background: #f1f5f9;
  }

  /* Performance optimizations for large datasets */
  .table-row-optimized {
    contain: layout style paint;
    will-change: transform;
  }

  /* Sorting indicators */
  .sort-icon-active {
    color: #2563eb;
  }

  .sort-icon-inactive {
    color: #94a3b8;
    transition: color 0.2s ease;
  }

  .sort-icon-inactive:hover {
    color: #64748b;
  }

  /* Table cell text handling */
  .table-cell-no-wrap {
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
  }

  /* Sticky elements with proper z-index */
  .sticky-header {
    position: sticky !important;
    top: 0 !important;
    z-index: 20 !important;
    background: white !important;
    border-bottom: 1px solid #e2e8f0;
  }

  /* Enhanced sticky header for detailed tables */
  .detailed-table-header {
    position: sticky !important;
    top: 0 !important;
    z-index: 25 !important;
    background: white !important;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  }

  .detailed-table-header th {
    position: sticky !important;
    top: 0 !important;
    z-index: 24 !important;
    background: white !important;
  }

  .sticky-pagination {
    position: sticky;
    top: 120px;
    z-index: 15;
    background: white;
    border-bottom: 1px solid #e2e8f0;
  }

  .sticky-scrollbar {
    position: sticky;
    bottom: 0;
    z-index: 10;
    background: white;
    border-top: 1px solid #e2e8f0;
  }

  /* Column resize cursor */
  .col-resize-cursor {
    cursor: col-resize !important;
  }

  /* Prevent text selection during resize */
  .no-select {
    user-select: none !important;
    -webkit-user-select: none !important;
    -moz-user-select: none !important;
    -ms-user-select: none !important;
  }

  /* Table header improvements */
  .table-header-cell {
    position: relative;
    background: #f8fafc;
    border-right: 1px solid #e2e8f0;
  }

  .table-header-cell:last-child {
    border-right: none;
  }

  /* Resize handle styling */
  .resize-handle {
    position: absolute;
    right: -2px;
    top: 0;
    bottom: 0;
    width: 4px;
    cursor: col-resize;
    background: transparent;
    transition: background-color 0.2s;
  }

  .resize-handle:hover {
    background: #3b82f6;
  }

  /* Table container improvements */
  .table-container {
    position: relative;
    border: 1px solid #e2e8f0;
    border-radius: 8px;
    overflow: hidden;
  }

  /* Smooth transition animations for detailed review */
  .row-transition-enter {
    opacity: 0;
    transform: translateY(-10px);
  }

  .row-transition-enter-active {
    opacity: 1;
    transform: translateY(0);
    transition: opacity 300ms ease-in-out, transform 300ms ease-in-out;
  }

  .row-transition-exit {
    opacity: 1;
    transform: translateY(0);
  }

  .row-transition-exit-active {
    opacity: 0;
    transform: translateY(-10px);
    transition: opacity 300ms ease-in-out, transform 300ms ease-in-out;
  }

  /* Row alignment animation */
  .row-align-animation {
    animation: rowAlign 0.5s cubic-bezier(0.4, 0, 0.2, 1);
  }

  @keyframes rowAlign {
    0% {
      transform: translateX(0);
      box-shadow: none;
    }
    50% {
      transform: translateX(8px);
      box-shadow: 0 4px 12px rgba(59, 130, 246, 0.3);
    }
    100% {
      transform: translateX(0);
      box-shadow: 0 2px 8px rgba(59, 130, 246, 0.2);
    }
  }

  /* Filter change animation */
  .filter-change-animation {
    animation: filterChange 0.4s ease-in-out;
  }

  @keyframes filterChange {
    0% {
      opacity: 0.7;
      transform: scale(0.98);
    }
    50% {
      opacity: 0.9;
      transform: scale(1.01);
    }
    100% {
      opacity: 1;
      transform: scale(1);
    }
  }



  /* Pending row highlight animation */
  .pending-row-highlight {
    animation: pendingHighlight 1s ease-in-out infinite;
  }

  @keyframes pendingHighlight {
    0%, 100% {
      background-color: rgba(59, 130, 246, 0.1);
      box-shadow: 0 0 0 1px rgba(59, 130, 246, 0.2);
    }
    50% {
      background-color: rgba(59, 130, 246, 0.2);
      box-shadow: 0 0 0 2px rgba(59, 130, 246, 0.4);
    }
  }
}